package com.itheima.sfbx.framework.commons.constant.warranty;

/**
 * WarrantyConstant
 *
 * @author: wgl
 * @describe: 保险订单常量
 * @date: 2022/12/28 10:10
 */
public class WarrantyOrderConstant {

    //状态（0待付款 1已付款 2逾期 3补缴 4付款失败 5付款中）
    public final static String ORDER_STATE_0="0";
    public final static String ORDER_STATE_1="1";
    public final static String ORDER_STATE_2="2";
    public final static String ORDER_STATE_3="3";
    public final static String ORDER_STATE_4="4";
    public final static String ORDER_STATE_5="5";
}
