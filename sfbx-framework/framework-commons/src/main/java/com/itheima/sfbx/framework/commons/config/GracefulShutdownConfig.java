package com.itheima.sfbx.framework.commons.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.servlet.server.ConfigurableServletWebServerFactory;

import javax.annotation.PreDestroy;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @Description 优雅关闭配置
 * 解决应用程序关闭时的内存泄漏问题
 */
@Slf4j
@Configuration
public class GracefulShutdownConfig {

    /**
     * 配置Tomcat优雅关闭
     */
    @Bean
    public ConfigurableServletWebServerFactory webServerFactory() {
        TomcatServletWebServerFactory factory = new TomcatServletWebServerFactory();
        factory.addConnectorCustomizers(connector -> {
            connector.setProperty("connectionTimeout", "20000");
            connector.setProperty("maxThreads", "200");
            connector.setProperty("minSpareThreads", "10");
        });
        return factory;
    }

    /**
     * 应用程序关闭时的清理工作
     */
    @PreDestroy
    public void onDestroy() {
        log.info("应用程序正在关闭，执行清理工作...");
        
        try {
            // 等待一段时间让正在处理的请求完成
            Thread.sleep(5000);
            
            // 关闭线程池
            shutdownThreadPools();
            
            // 清理Nacos相关资源
            cleanupNacosResources();
            
            log.info("应用程序清理工作完成");
        } catch (Exception e) {
            log.error("应用程序关闭时清理工作出现异常", e);
        }
    }

    /**
     * 关闭线程池
     */
    private void shutdownThreadPools() {
        try {
            // 这里可以添加具体的线程池关闭逻辑
            log.info("正在关闭线程池...");
            
            // 获取所有活跃线程并尝试优雅关闭
            ThreadGroup rootGroup = Thread.currentThread().getThreadGroup();
            ThreadGroup parentGroup;
            while ((parentGroup = rootGroup.getParent()) != null) {
                rootGroup = parentGroup;
            }
            
            Thread[] threads = new Thread[rootGroup.activeCount()];
            rootGroup.enumerate(threads);
            
            for (Thread thread : threads) {
                if (thread != null && thread.getName().contains("nacos")) {
                    log.info("尝试中断Nacos相关线程: {}", thread.getName());
                    thread.interrupt();
                }
            }
            
        } catch (Exception e) {
            log.error("关闭线程池时出现异常", e);
        }
    }

    /**
     * 清理Nacos相关资源
     */
    private void cleanupNacosResources() {
        try {
            log.info("正在清理Nacos相关资源...");
            
            // 这里可以添加具体的Nacos资源清理逻辑
            // 例如：关闭Nacos客户端连接等
            
        } catch (Exception e) {
            log.error("清理Nacos资源时出现异常", e);
        }
    }
}
