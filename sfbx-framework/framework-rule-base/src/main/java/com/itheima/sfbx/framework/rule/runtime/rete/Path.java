/*******************************************************************************
 * Copyright 2017 Bstek
 * 
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License.  You may obtain a copy
 * of the License at
 * 
 *   http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations under
 * the License.
 ******************************************************************************/
package com.itheima.sfbx.framework.rule.runtime.rete;

/**
 * <AUTHOR>
 * @since 2015年1月8日
 */
public class Path {
	private boolean passed;
	private Activity to;
	public Path(Activity to) {
		this.to = to;
		if(to instanceof AndActivity){
			AndActivity andActivity=(AndActivity)to;
			andActivity.addFromPath(this);
		}
	}
	public Activity getTo() {
		return to;
	}
	public boolean isPassed() {
		return passed;
	}
	public void setPassed(boolean passed) {
		this.passed = passed;
	}
}
