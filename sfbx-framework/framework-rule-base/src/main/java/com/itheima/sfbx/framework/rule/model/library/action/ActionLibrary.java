/*******************************************************************************
 * Copyright 2017 Bstek
 * 
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License.  You may obtain a copy
 * of the License at
 * 
 *   http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations under
 * the License.
 ******************************************************************************/
package com.itheima.sfbx.framework.rule.model.library.action;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2014年12月29日
 */
public class ActionLibrary {
	private List<SpringBean> springBeans;
	public List<SpringBean> getSpringBeans() {
		return springBeans;
	}
	
	public void setSpringBeans(List<SpringBean> springBeans) {
		this.springBeans = springBeans;
	}

	public void addSpringBean(SpringBean springBean) {
		if(this.springBeans==null){
			this.springBeans=new ArrayList<SpringBean>();
		}
		this.springBeans.add(springBean);
	}
}
