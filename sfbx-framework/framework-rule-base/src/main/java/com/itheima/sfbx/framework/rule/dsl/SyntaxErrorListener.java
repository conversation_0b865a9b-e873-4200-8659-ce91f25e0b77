/*******************************************************************************
 * Copyright 2017 Bstek
 * 
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License.  You may obtain a copy
 * of the License at
 * 
 *   http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations under
 * the License.
 ******************************************************************************/
package com.itheima.sfbx.framework.rule.dsl;

import org.antlr.v4.runtime.BaseErrorListener;
import org.antlr.v4.runtime.RecognitionException;
import org.antlr.v4.runtime.Recognizer;

/**
 * <AUTHOR>
 * @since 2015年2月27日
 */
public class SyntaxErrorListener extends BaseErrorListener {
	private SyntaxErrorReportor reportor;
	public SyntaxErrorListener(SyntaxErrorReportor reportor) {
		this.reportor=reportor;
	}
	@Override
	public void syntaxError(Recognizer<?, ?> recognizer,
			Object offendingSymbol, int line, int charPositionInLine,
			String msg, RecognitionException e) {
		this.reportor.addError(line, charPositionInLine, offendingSymbol, msg);
		//super.syntaxError(recognizer, offendingSymbol, line, charPositionInLine, msg, e);
	}
}
