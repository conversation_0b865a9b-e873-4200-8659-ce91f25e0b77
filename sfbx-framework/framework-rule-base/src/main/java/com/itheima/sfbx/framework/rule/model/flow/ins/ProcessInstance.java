/*******************************************************************************
 * Copyright 2017 Bstek
 * 
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License.  You may obtain a copy
 * of the License at
 * 
 *   http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations under
 * the License.
 ******************************************************************************/
package com.itheima.sfbx.framework.rule.model.flow.ins;

import com.itheima.sfbx.framework.rule.model.flow.FlowNode;
import com.itheima.sfbx.framework.rule.model.flow.ProcessDefinition;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2015年7月20日
 */
public interface ProcessInstance {
	ProcessDefinition getProcessDefinition();
	List<ProcessInstance> getChildren();
	int getParallelInstanceCount();
	String getId();
	FlowNode getCurrentNode();
	ProcessInstance getParent();
}
