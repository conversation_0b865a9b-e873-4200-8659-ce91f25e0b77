/*******************************************************************************
 * Copyright 2017 Bstek
 * 
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License.  You may obtain a copy
 * of the License at
 * 
 *   http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations under
 * the License.
 ******************************************************************************/
package com.itheima.sfbx.framework.rule.runtime.cache;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import java.util.Collection;

/**
 * <AUTHOR>
 * @since 2016年12月29日
 */
public class CacheUtils implements ApplicationContextAware{
	private static KnowledgeCache knowledgeCache;
	public static KnowledgeCache getKnowledgeCache() {
		return knowledgeCache;
	}
	@Override
	public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
		Collection<KnowledgeCache> caches=applicationContext.getBeansOfType(KnowledgeCache.class).values();
		if(caches.size()>0){
			CacheUtils.knowledgeCache=caches.iterator().next();
		}else{
			CacheUtils.knowledgeCache=new MemoryKnowledgeCache();
		}
	}
}
