/*******************************************************************************
 * Copyright 2017 Bstek
 * 
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License.  You may obtain a copy
 * of the License at
 * 
 *   http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations under
 * the License.
 ******************************************************************************/
package com.itheima.sfbx.framework.rule.model.rule.lhs;

import com.itheima.sfbx.framework.rule.RuleException;
import com.itheima.sfbx.framework.rule.Utils;
import com.itheima.sfbx.framework.rule.action.ActionValue;
import com.itheima.sfbx.framework.rule.action.ExecuteMethodAction;
import com.itheima.sfbx.framework.rule.model.library.Datatype;
import com.itheima.sfbx.framework.rule.model.rule.Op;
import com.itheima.sfbx.framework.rule.model.rule.SimpleArithmetic;
import com.itheima.sfbx.framework.rule.model.rule.Value;
import com.itheima.sfbx.framework.rule.runtime.assertor.AssertorEvaluator;
import com.itheima.sfbx.framework.rule.runtime.rete.EvaluationContext;
import com.itheima.sfbx.framework.rule.runtime.rete.ValueCompute;
import org.codehaus.jackson.annotate.JsonIgnore;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2014年12月29日
 */
public class Criteria extends BaseCriterion implements BaseCriteria{
	@JsonIgnore
	private String id;
	private Op op;
	private Left left;
	private Value value;
	
	@Override
	public EvaluateResponse evaluate(EvaluationContext context,Object obj,List<Object> allMatchedObjects){
		Object leftValue=null;
		Datatype datatype=null;
		LeftPart leftPart=left.getLeftPart();
		if(leftPart instanceof VariableLeftPart){
			VariableLeftPart varPart=(VariableLeftPart)leftPart;
			datatype=varPart.getDatatype();
			if(varPart.getVariableName()==null){
				throw new RuleException("Criteria left[variableName] can not be null.");
			}
			leftValue=Utils.getObjectProperty(obj, varPart.getVariableName());
		}else{
			if(leftPart instanceof MethodLeftPart){
				MethodLeftPart methodPart=(MethodLeftPart)leftPart;
				ExecuteMethodAction methodAction=new ExecuteMethodAction();
				methodAction.setBeanId(methodPart.getBeanId());
				methodAction.setBeanLabel(methodPart.getBeanLabel());
				methodAction.setMethodLabel(methodPart.getMethodLabel());
				methodAction.setMethodName(methodPart.getMethodName());
				methodAction.setParameters(methodPart.getParameters());
				ActionValue actionValue=methodAction.execute(context, obj,allMatchedObjects,null);
				if(actionValue==null){
					leftValue=null;
				}else{
					leftValue=actionValue.getValue();
				}
			}else if(leftPart instanceof ExistLeftPart){
				ExistLeftPart existPart=(ExistLeftPart)leftPart;
				leftValue=existPart.evaluate(context, obj, allMatchedObjects);
			}else if(leftPart instanceof AllLeftPart){
				AllLeftPart allPart=(AllLeftPart)leftPart;
				leftValue=allPart.evaluate(context, obj, allMatchedObjects);
			}else if(leftPart instanceof CollectLeftPart){
				CollectLeftPart collectPart=(CollectLeftPart)leftPart;
				leftValue=collectPart.evaluate(context, obj, allMatchedObjects);
			}else if(leftPart instanceof CommonFunctionLeftPart){
				CommonFunctionLeftPart part=(CommonFunctionLeftPart)leftPart;
				leftValue=part.evaluate(context, obj, allMatchedObjects);
			}
			datatype=Utils.getDatatype(leftValue);
		}
		Object leftResult=leftValue;
		SimpleArithmetic arithmetic=left.getArithmetic();
		ValueCompute valueCompute=context.getValueCompute();
		if(arithmetic!=null){
			leftResult=valueCompute.simpleArithmeticCompute(context,leftValue, arithmetic);
		}
		EvaluateResponse response=new EvaluateResponse();
		response.setLeftResult(leftResult);
		Object right=null;
		if(value!=null){
			right=valueCompute.complexValueCompute(value,obj,context,allMatchedObjects,null);
			response.setRightResult(right);
			if(right==null){
				response.setResult(false);
				return response;
			}
		}
		AssertorEvaluator assertorEvaluator=context.getAssertorEvaluator();
		boolean result=assertorEvaluator.evaluate(leftResult, right, datatype,op);
		response.setResult(result);
		return response;
	}
	@Override
	public String getId() {
		if(id==null){
			id=left.getLeftPart().getId()+"【"+op.toString()+"】";
			if(value!=null)id+=value.getId();
		}
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public Op getOp() {
		return op;
	}
	public void setOp(Op op) {
		this.op = op;
	}
	public Left getLeft() {
		return left;
	}
	public void setLeft(Left left) {
		this.left = left;
	}
	public Value getValue() {
		return value;
	}
	public void setValue(Value value) {
		this.value = value;
	}
}
