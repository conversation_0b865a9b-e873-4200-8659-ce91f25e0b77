// Generated from RuleLexer.g4 by ANTLR 4.5.3
package com.itheima.sfbx.framework.rule.dsl;

import org.antlr.v4.runtime.*;
import org.antlr.v4.runtime.atn.ATN;
import org.antlr.v4.runtime.atn.ATNDeserializer;
import org.antlr.v4.runtime.atn.LexerATNSimulator;
import org.antlr.v4.runtime.atn.PredictionContextCache;
import org.antlr.v4.runtime.dfa.DFA;

@SuppressWarnings({"all", "warnings", "unchecked", "unused", "cast"})
public class RuleLexer extends Lexer {
	static { RuntimeMetaData.checkVersion("4.5.3", RuntimeMetaData.VERSION); }

	protected static final DFA[] _decisionToDFA;
	protected static final PredictionContextCache _sharedContextCache =
		new PredictionContextCache();
	public static final int
		COUNT=1, AVG=2, SUM=3, MAX=4, MIN=5, AND=6, OR=7, Datatype=8, GreaterThen=9, 
		GreaterThenOrEquals=10, LessThen=11, LessThenOrEquals=12, Equals=13, NotEquals=14, 
		EndWith=15, NotEndWith=16, StartWith=17, NotStartWith=18, In=19, NotIn=20, 
		Match=21, NotMatch=22, Contain=23, NotContain=24, EqualsIgnoreCase=25, 
		NotEqualsIgnoreCase=26, ARITH=27, NUMBER=28, Boolean=29, Identifier=30, 
		STRING=31, WS=32, NL=33, COMMENT=34, LINE_COMMENT=35;
	public static String[] modeNames = {
		"DEFAULT_MODE"
	};

	public static final String[] ruleNames = {
		"COUNT", "AVG", "SUM", "MAX", "MIN", "AND", "OR", "Datatype", "GreaterThen", 
		"GreaterThenOrEquals", "LessThen", "LessThenOrEquals", "Equals", "NotEquals", 
		"EndWith", "NotEndWith", "StartWith", "NotStartWith", "In", "NotIn", "Match", 
		"NotMatch", "Contain", "NotContain", "EqualsIgnoreCase", "NotEqualsIgnoreCase", 
		"ARITH", "NUMBER", "Boolean", "Identifier", "STRING", "STRING_CONTENT", 
		"INT", "EXP", "EscapeSequence", "OctalEscape", "UnicodeEscape", "Char", 
		"StartChar", "DIGIT", "HEX", "WS", "NL", "COMMENT", "LINE_COMMENT"
	};

	private static final String[] _LITERAL_NAMES = {
		null, "'count'", "'avg'", "'sum'", "'max'", "'min'"
	};
	private static final String[] _SYMBOLIC_NAMES = {
		null, "COUNT", "AVG", "SUM", "MAX", "MIN", "AND", "OR", "Datatype", "GreaterThen", 
		"GreaterThenOrEquals", "LessThen", "LessThenOrEquals", "Equals", "NotEquals", 
		"EndWith", "NotEndWith", "StartWith", "NotStartWith", "In", "NotIn", "Match", 
		"NotMatch", "Contain", "NotContain", "EqualsIgnoreCase", "NotEqualsIgnoreCase", 
		"ARITH", "NUMBER", "Boolean", "Identifier", "STRING", "WS", "NL", "COMMENT", 
		"LINE_COMMENT"
	};
	public static final Vocabulary VOCABULARY = new VocabularyImpl(_LITERAL_NAMES, _SYMBOLIC_NAMES);

	/**
	 * @deprecated Use {@link #VOCABULARY} instead.
	 */
	@Deprecated
	public static final String[] tokenNames;
	static {
		tokenNames = new String[_SYMBOLIC_NAMES.length];
		for (int i = 0; i < tokenNames.length; i++) {
			tokenNames[i] = VOCABULARY.getLiteralName(i);
			if (tokenNames[i] == null) {
				tokenNames[i] = VOCABULARY.getSymbolicName(i);
			}

			if (tokenNames[i] == null) {
				tokenNames[i] = "<INVALID>";
			}
		}
	}

	@Override
	@Deprecated
	public String[] getTokenNames() {
		return tokenNames;
	}

	@Override

	public Vocabulary getVocabulary() {
		return VOCABULARY;
	}


	public RuleLexer(CharStream input) {
		super(input);
		_interp = new LexerATNSimulator(this,_ATN,_decisionToDFA,_sharedContextCache);
	}

	@Override
	public String getGrammarFileName() { return "RuleLexer.g4"; }

	@Override
	public String[] getRuleNames() { return ruleNames; }

	@Override
	public String getSerializedATN() { return _serializedATN; }

	@Override
	public String[] getModeNames() { return modeNames; }

	@Override
	public ATN getATN() { return _ATN; }

	public static final String _serializedATN =
		"\3\u0430\ud6d1\u8206\uad2d\u4417\uaef1\u8d80\uaadd\2%\u0254\b\1\4\2\t"+
		"\2\4\3\t\3\4\4\t\4\4\5\t\5\4\6\t\6\4\7\t\7\4\b\t\b\4\t\t\t\4\n\t\n\4\13"+
		"\t\13\4\f\t\f\4\r\t\r\4\16\t\16\4\17\t\17\4\20\t\20\4\21\t\21\4\22\t\22"+
		"\4\23\t\23\4\24\t\24\4\25\t\25\4\26\t\26\4\27\t\27\4\30\t\30\4\31\t\31"+
		"\4\32\t\32\4\33\t\33\4\34\t\34\4\35\t\35\4\36\t\36\4\37\t\37\4 \t \4!"+
		"\t!\4\"\t\"\4#\t#\4$\t$\4%\t%\4&\t&\4\'\t\'\4(\t(\4)\t)\4*\t*\4+\t+\4"+
		",\t,\4-\t-\4.\t.\3\2\3\2\3\2\3\2\3\2\3\2\3\3\3\3\3\3\3\3\3\4\3\4\3\4\3"+
		"\4\3\5\3\5\3\5\3\5\3\6\3\6\3\6\3\6\3\7\3\7\3\7\3\7\3\7\3\7\3\7\3\7\3\7"+
		"\5\7}\n\7\3\b\3\b\3\b\3\b\3\b\3\b\3\b\5\b\u0086\n\b\3\t\3\t\3\t\3\t\3"+
		"\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t"+
		"\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3"+
		"\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t"+
		"\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3"+
		"\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t\3\t"+
		"\3\t\3\t\5\t\u00e6\n\t\3\n\3\n\3\n\5\n\u00eb\n\n\3\13\3\13\3\13\3\13\3"+
		"\13\3\13\5\13\u00f3\n\13\3\f\3\f\3\f\5\f\u00f8\n\f\3\r\3\r\3\r\3\r\3\r"+
		"\3\r\5\r\u0100\n\r\3\16\3\16\3\16\3\16\5\16\u0106\n\16\3\17\3\17\3\17"+
		"\3\17\3\17\5\17\u010d\n\17\3\20\3\20\3\20\3\20\3\20\3\20\3\20\3\20\3\20"+
		"\3\20\5\20\u0119\n\20\3\21\3\21\3\21\3\21\3\21\3\21\3\21\3\21\3\21\3\21"+
		"\3\21\3\21\3\21\3\21\5\21\u0129\n\21\3\22\3\22\3\22\3\22\3\22\3\22\3\22"+
		"\3\22\3\22\3\22\3\22\3\22\5\22\u0137\n\22\3\23\3\23\3\23\3\23\3\23\3\23"+
		"\3\23\3\23\3\23\3\23\3\23\3\23\3\23\3\23\3\23\3\23\5\23\u0149\n\23\3\24"+
		"\3\24\3\24\3\24\3\24\3\24\5\24\u0151\n\24\3\25\3\25\3\25\3\25\3\25\3\25"+
		"\3\25\3\25\3\25\3\25\5\25\u015d\n\25\3\26\3\26\3\26\3\26\3\26\3\26\3\26"+
		"\5\26\u0166\n\26\3\27\3\27\3\27\3\27\3\27\3\27\3\27\3\27\3\27\3\27\3\27"+
		"\5\27\u0173\n\27\3\30\3\30\3\30\3\30\3\30\3\30\3\30\3\30\3\30\5\30\u017e"+
		"\n\30\3\31\3\31\3\31\3\31\3\31\3\31\3\31\3\31\3\31\3\31\3\31\3\31\3\31"+
		"\5\31\u018d\n\31\3\32\3\32\3\32\3\32\3\32\3\32\3\32\3\32\3\32\3\32\3\32"+
		"\3\32\3\32\3\32\3\32\3\32\3\32\3\32\3\32\3\32\3\32\3\32\3\32\5\32\u01a6"+
		"\n\32\3\33\3\33\3\33\3\33\3\33\3\33\3\33\3\33\3\33\3\33\3\33\3\33\3\33"+
		"\3\33\3\33\3\33\3\33\3\33\3\33\3\33\3\33\3\33\3\33\3\33\3\33\3\33\3\33"+
		"\5\33\u01c3\n\33\3\34\3\34\3\35\5\35\u01c8\n\35\3\35\3\35\3\35\3\35\5"+
		"\35\u01ce\n\35\3\35\5\35\u01d1\n\35\3\35\3\35\3\35\3\35\5\35\u01d7\n\35"+
		"\3\35\5\35\u01da\n\35\3\36\3\36\3\36\3\36\3\36\3\36\3\36\3\36\3\36\5\36"+
		"\u01e5\n\36\3\37\3\37\7\37\u01e9\n\37\f\37\16\37\u01ec\13\37\3 \3 \3 "+
		"\3 \3!\3!\7!\u01f4\n!\f!\16!\u01f7\13!\3\"\6\"\u01fa\n\"\r\"\16\"\u01fb"+
		"\3#\3#\5#\u0200\n#\3#\3#\3$\3$\3$\3$\5$\u0208\n$\3%\3%\3%\3%\3%\3%\3%"+
		"\3%\3%\5%\u0213\n%\3&\3&\3&\3&\3&\3&\3&\3\'\3\'\3\'\3\'\5\'\u0220\n\'"+
		"\3(\5(\u0223\n(\3)\3)\3*\3*\3+\6+\u022a\n+\r+\16+\u022b\3+\3+\3,\5,\u0231"+
		"\n,\3,\3,\3,\3,\3-\3-\3-\3-\7-\u023b\n-\f-\16-\u023e\13-\3-\3-\3-\3-\3"+
		"-\3.\3.\3.\3.\7.\u0249\n.\f.\16.\u024c\13.\3.\5.\u024f\n.\3.\3.\3.\3."+
		"\3\u023c\2/\3\3\5\4\7\5\t\6\13\7\r\b\17\t\21\n\23\13\25\f\27\r\31\16\33"+
		"\17\35\20\37\21!\22#\23%\24\'\25)\26+\27-\30/\31\61\32\63\33\65\34\67"+
		"\359\36;\37= ?!A\2C\2E\2G\2I\2K\2M\2O\2Q\2S\2U\"W#Y$[%\3\2\16\6\2\'\'"+
		",-//\61\61\3\2$$\4\2GGgg\4\2--//\n\2$$))^^ddhhppttvv\4\2//aa\5\2\u00b9"+
		"\u00b9\u0302\u0371\u2041\u2042\t\2C\\c|\u2072\u2191\u2c02\u2ff1\u3003"+
		"\ud801\uf902\ufdd1\ufdf2\uffff\3\2\62;\5\2\62;CHch\5\2\13\f\17\17\"\""+
		"\4\2\f\f\17\17\u028b\2\3\3\2\2\2\2\5\3\2\2\2\2\7\3\2\2\2\2\t\3\2\2\2\2"+
		"\13\3\2\2\2\2\r\3\2\2\2\2\17\3\2\2\2\2\21\3\2\2\2\2\23\3\2\2\2\2\25\3"+
		"\2\2\2\2\27\3\2\2\2\2\31\3\2\2\2\2\33\3\2\2\2\2\35\3\2\2\2\2\37\3\2\2"+
		"\2\2!\3\2\2\2\2#\3\2\2\2\2%\3\2\2\2\2\'\3\2\2\2\2)\3\2\2\2\2+\3\2\2\2"+
		"\2-\3\2\2\2\2/\3\2\2\2\2\61\3\2\2\2\2\63\3\2\2\2\2\65\3\2\2\2\2\67\3\2"+
		"\2\2\29\3\2\2\2\2;\3\2\2\2\2=\3\2\2\2\2?\3\2\2\2\2U\3\2\2\2\2W\3\2\2\2"+
		"\2Y\3\2\2\2\2[\3\2\2\2\3]\3\2\2\2\5c\3\2\2\2\7g\3\2\2\2\tk\3\2\2\2\13"+
		"o\3\2\2\2\r|\3\2\2\2\17\u0085\3\2\2\2\21\u00e5\3\2\2\2\23\u00ea\3\2\2"+
		"\2\25\u00f2\3\2\2\2\27\u00f7\3\2\2\2\31\u00ff\3\2\2\2\33\u0105\3\2\2\2"+
		"\35\u010c\3\2\2\2\37\u0118\3\2\2\2!\u0128\3\2\2\2#\u0136\3\2\2\2%\u0148"+
		"\3\2\2\2\'\u0150\3\2\2\2)\u015c\3\2\2\2+\u0165\3\2\2\2-\u0172\3\2\2\2"+
		"/\u017d\3\2\2\2\61\u018c\3\2\2\2\63\u01a5\3\2\2\2\65\u01c2\3\2\2\2\67"+
		"\u01c4\3\2\2\29\u01d9\3\2\2\2;\u01e4\3\2\2\2=\u01e6\3\2\2\2?\u01ed\3\2"+
		"\2\2A\u01f5\3\2\2\2C\u01f9\3\2\2\2E\u01fd\3\2\2\2G\u0207\3\2\2\2I\u0212"+
		"\3\2\2\2K\u0214\3\2\2\2M\u021f\3\2\2\2O\u0222\3\2\2\2Q\u0224\3\2\2\2S"+
		"\u0226\3\2\2\2U\u0229\3\2\2\2W\u0230\3\2\2\2Y\u0236\3\2\2\2[\u0244\3\2"+
		"\2\2]^\7e\2\2^_\7q\2\2_`\7w\2\2`a\7p\2\2ab\7v\2\2b\4\3\2\2\2cd\7c\2\2"+
		"de\7x\2\2ef\7i\2\2f\6\3\2\2\2gh\7u\2\2hi\7w\2\2ij\7o\2\2j\b\3\2\2\2kl"+
		"\7o\2\2lm\7c\2\2mn\7z\2\2n\n\3\2\2\2op\7o\2\2pq\7k\2\2qr\7p\2\2r\f\3\2"+
		"\2\2st\7c\2\2tu\7p\2\2u}\7f\2\2vw\7(\2\2w}\7(\2\2x}\7.\2\2yz\7\u5e78\2"+
		"\2z}\7\u4e16\2\2{}\7\u4e16\2\2|s\3\2\2\2|v\3\2\2\2|x\3\2\2\2|y\3\2\2\2"+
		"|{\3\2\2\2}\16\3\2\2\2~\177\7q\2\2\177\u0086\7t\2\2\u0080\u0081\7~\2\2"+
		"\u0081\u0086\7~\2\2\u0082\u0083\7\u6218\2\2\u0083\u0086\7\u8007\2\2\u0084"+
		"\u0086\7\u6218\2\2\u0085~\3\2\2\2\u0085\u0080\3\2\2\2\u0085\u0082\3\2"+
		"\2\2\u0085\u0084\3\2\2\2\u0086\20\3\2\2\2\u0087\u0088\7U\2\2\u0088\u0089"+
		"\7v\2\2\u0089\u008a\7t\2\2\u008a\u008b\7k\2\2\u008b\u008c\7p\2\2\u008c"+
		"\u00e6\7i\2\2\u008d\u008e\7k\2\2\u008e\u008f\7p\2\2\u008f\u00e6\7v\2\2"+
		"\u0090\u0091\7K\2\2\u0091\u0092\7p\2\2\u0092\u0093\7v\2\2\u0093\u0094"+
		"\7g\2\2\u0094\u0095\7i\2\2\u0095\u0096\7g\2\2\u0096\u00e6\7t\2\2\u0097"+
		"\u0098\7f\2\2\u0098\u0099\7q\2\2\u0099\u009a\7w\2\2\u009a\u009b\7d\2\2"+
		"\u009b\u009c\7n\2\2\u009c\u00e6\7g\2\2\u009d\u009e\7F\2\2\u009e\u009f"+
		"\7q\2\2\u009f\u00a0\7w\2\2\u00a0\u00a1\7d\2\2\u00a1\u00a2\7n\2\2\u00a2"+
		"\u00e6\7g\2\2\u00a3\u00a4\7n\2\2\u00a4\u00a5\7q\2\2\u00a5\u00a6\7p\2\2"+
		"\u00a6\u00e6\7i\2\2\u00a7\u00a8\7N\2\2\u00a8\u00a9\7q\2\2\u00a9\u00aa"+
		"\7p\2\2\u00aa\u00e6\7i\2\2\u00ab\u00ac\7h\2\2\u00ac\u00ad\7n\2\2\u00ad"+
		"\u00ae\7q\2\2\u00ae\u00af\7c\2\2\u00af\u00e6\7v\2\2\u00b0\u00b1\7H\2\2"+
		"\u00b1\u00b2\7n\2\2\u00b2\u00b3\7q\2\2\u00b3\u00b4\7c\2\2\u00b4\u00e6"+
		"\7v\2\2\u00b5\u00b6\7D\2\2\u00b6\u00b7\7k\2\2\u00b7\u00b8\7i\2\2\u00b8"+
		"\u00b9\7F\2\2\u00b9\u00ba\7g\2\2\u00ba\u00bb\7e\2\2\u00bb\u00bc\7k\2\2"+
		"\u00bc\u00bd\7o\2\2\u00bd\u00be\7c\2\2\u00be\u00e6\7n\2\2\u00bf\u00c0"+
		"\7d\2\2\u00c0\u00c1\7q\2\2\u00c1\u00c2\7q\2\2\u00c2\u00c3\7n\2\2\u00c3"+
		"\u00c4\7g\2\2\u00c4\u00c5\7c\2\2\u00c5\u00e6\7p\2\2\u00c6\u00c7\7D\2\2"+
		"\u00c7\u00c8\7q\2\2\u00c8\u00c9\7q\2\2\u00c9\u00ca\7n\2\2\u00ca\u00cb"+
		"\7g\2\2\u00cb\u00cc\7c\2\2\u00cc\u00e6\7p\2\2\u00cd\u00ce\7F\2\2\u00ce"+
		"\u00cf\7c\2\2\u00cf\u00d0\7v\2\2\u00d0\u00e6\7g\2\2\u00d1\u00d2\7N\2\2"+
		"\u00d2\u00d3\7k\2\2\u00d3\u00d4\7u\2\2\u00d4\u00e6\7v\2\2\u00d5\u00d6"+
		"\7U\2\2\u00d6\u00d7\7g\2\2\u00d7\u00e6\7v\2\2\u00d8\u00d9\7O\2\2\u00d9"+
		"\u00da\7c\2\2\u00da\u00e6\7r\2\2\u00db\u00dc\7G\2\2\u00dc\u00dd\7p\2\2"+
		"\u00dd\u00de\7w\2\2\u00de\u00e6\7o\2\2\u00df\u00e0\7Q\2\2\u00e0\u00e1"+
		"\7d\2\2\u00e1\u00e2\7l\2\2\u00e2\u00e3\7g\2\2\u00e3\u00e4\7e\2\2\u00e4"+
		"\u00e6\7v\2\2\u00e5\u0087\3\2\2\2\u00e5\u008d\3\2\2\2\u00e5\u0090\3\2"+
		"\2\2\u00e5\u0097\3\2\2\2\u00e5\u009d\3\2\2\2\u00e5\u00a3\3\2\2\2\u00e5"+
		"\u00a7\3\2\2\2\u00e5\u00ab\3\2\2\2\u00e5\u00b0\3\2\2\2\u00e5\u00b5\3\2"+
		"\2\2\u00e5\u00bf\3\2\2\2\u00e5\u00c6\3\2\2\2\u00e5\u00cd\3\2\2\2\u00e5"+
		"\u00d1\3\2\2\2\u00e5\u00d5\3\2\2\2\u00e5\u00d8\3\2\2\2\u00e5\u00db\3\2"+
		"\2\2\u00e5\u00df\3\2\2\2\u00e6\22\3\2\2\2\u00e7\u00eb\7@\2\2\u00e8\u00e9"+
		"\7\u5929\2\2\u00e9\u00eb\7\u4e90\2\2\u00ea\u00e7\3\2\2\2\u00ea\u00e8\3"+
		"\2\2\2\u00eb\24\3\2\2\2\u00ec\u00ed\7@\2\2\u00ed\u00f3\7?\2\2\u00ee\u00ef"+
		"\7\u5929\2\2\u00ef\u00f0\7\u4e90\2\2\u00f0\u00f1\7\u7b4b\2\2\u00f1\u00f3"+
		"\7\u4e90\2\2\u00f2\u00ec\3\2\2\2\u00f2\u00ee\3\2\2\2\u00f3\26\3\2\2\2"+
		"\u00f4\u00f8\7>\2\2\u00f5\u00f6\7\u5c11\2\2\u00f6\u00f8\7\u4e90\2\2\u00f7"+
		"\u00f4\3\2\2\2\u00f7\u00f5\3\2\2\2\u00f8\30\3\2\2\2\u00f9\u00fa\7>\2\2"+
		"\u00fa\u0100\7?\2\2\u00fb\u00fc\7\u5c11\2\2\u00fc\u00fd\7\u4e90\2\2\u00fd"+
		"\u00fe\7\u7b4b\2\2\u00fe\u0100\7\u4e90\2\2\u00ff\u00f9\3\2\2\2\u00ff\u00fb"+
		"\3\2\2\2\u0100\32\3\2\2\2\u0101\u0102\7?\2\2\u0102\u0106\7?\2\2\u0103"+
		"\u0104\7\u7b4b\2\2\u0104\u0106\7\u4e90\2\2\u0105\u0101\3\2\2\2\u0105\u0103"+
		"\3\2\2\2\u0106\34\3\2\2\2\u0107\u0108\7#\2\2\u0108\u010d\7?\2\2\u0109"+
		"\u010a\7\u4e0f\2\2\u010a\u010b\7\u7b4b\2\2\u010b\u010d\7\u4e90\2\2\u010c"+
		"\u0107\3\2\2\2\u010c\u0109\3\2\2\2\u010d\36\3\2\2\2\u010e\u010f\7G\2\2"+
		"\u010f\u0110\7p\2\2\u0110\u0111\7f\2\2\u0111\u0112\7Y\2\2\u0112\u0113"+
		"\7k\2\2\u0113\u0114\7v\2\2\u0114\u0119\7j\2\2\u0115\u0116\7\u7ed5\2\2"+
		"\u0116\u0117\7\u6761\2\2\u0117\u0119\7\u4e90\2\2\u0118\u010e\3\2\2\2\u0118"+
		"\u0115\3\2\2\2\u0119 \3\2\2\2\u011a\u011b\7P\2\2\u011b\u011c\7q\2\2\u011c"+
		"\u011d\7v\2\2\u011d\u011e\7G\2\2\u011e\u011f\7p\2\2\u011f\u0120\7f\2\2"+
		"\u0120\u0121\7Y\2\2\u0121\u0122\7k\2\2\u0122\u0123\7v\2\2\u0123\u0129"+
		"\7j\2\2\u0124\u0125\7\u4e0f\2\2\u0125\u0126\7\u7ed5\2\2\u0126\u0127\7"+
		"\u6761\2\2\u0127\u0129\7\u4e90\2\2\u0128\u011a\3\2\2\2\u0128\u0124\3\2"+
		"\2\2\u0129\"\3\2\2\2\u012a\u012b\7U\2\2\u012b\u012c\7v\2\2\u012c\u012d"+
		"\7c\2\2\u012d\u012e\7t\2\2\u012e\u012f\7v\2\2\u012f\u0130\7Y\2\2\u0130"+
		"\u0131\7k\2\2\u0131\u0132\7v\2\2\u0132\u0137\7j\2\2\u0133\u0134\7\u5f02"+
		"\2\2\u0134\u0135\7\u59cd\2\2\u0135\u0137\7\u4e90\2\2\u0136\u012a\3\2\2"+
		"\2\u0136\u0133\3\2\2\2\u0137$\3\2\2\2\u0138\u0139\7P\2\2\u0139\u013a\7"+
		"q\2\2\u013a\u013b\7v\2\2\u013b\u013c\7U\2\2\u013c\u013d\7v\2\2\u013d\u013e"+
		"\7c\2\2\u013e\u013f\7t\2\2\u013f\u0140\7v\2\2\u0140\u0141\7Y\2\2\u0141"+
		"\u0142\7k\2\2\u0142\u0143\7v\2\2\u0143\u0149\7j\2\2\u0144\u0145\7\u4e0f"+
		"\2\2\u0145\u0146\7\u5f02\2\2\u0146\u0147\7\u59cd\2\2\u0147\u0149\7\u4e90"+
		"\2\2\u0148\u0138\3\2\2\2\u0148\u0144\3\2\2\2\u0149&\3\2\2\2\u014a\u014b"+
		"\7K\2\2\u014b\u0151\7p\2\2\u014c\u014d\7\u572a\2\2\u014d\u014e\7\u96c8"+
		"\2\2\u014e\u014f\7\u540a\2\2\u014f\u0151\7\u4e2f\2\2\u0150\u014a\3\2\2"+
		"\2\u0150\u014c\3\2\2\2\u0151(\3\2\2\2\u0152\u0153\7P\2\2\u0153\u0154\7"+
		"q\2\2\u0154\u0155\7v\2\2\u0155\u0156\7K\2\2\u0156\u015d\7p\2\2\u0157\u0158"+
		"\7\u4e0f\2\2\u0158\u0159\7\u572a\2\2\u0159\u015a\7\u96c8\2\2\u015a\u015b"+
		"\7\u540a\2\2\u015b\u015d\7\u4e2f\2\2\u015c\u0152\3\2\2\2\u015c\u0157\3"+
		"\2\2\2\u015d*\3\2\2\2\u015e\u015f\7O\2\2\u015f\u0160\7c\2\2\u0160\u0161"+
		"\7v\2\2\u0161\u0162\7e\2\2\u0162\u0166\7j\2\2\u0163\u0164\7\u533b\2\2"+
		"\u0164\u0166\7\u914f\2\2\u0165\u015e\3\2\2\2\u0165\u0163\3\2\2\2\u0166"+
		",\3\2\2\2\u0167\u0168\7P\2\2\u0168\u0169\7q\2\2\u0169\u016a\7v\2\2\u016a"+
		"\u016b\7O\2\2\u016b\u016c\7c\2\2\u016c\u016d\7v\2\2\u016d\u016e\7e\2\2"+
		"\u016e\u0173\7j\2\2\u016f\u0170\7\u4e0f\2\2\u0170\u0171\7\u533b\2\2\u0171"+
		"\u0173\7\u914f\2\2\u0172\u0167\3\2\2\2\u0172\u016f\3\2\2\2\u0173.\3\2"+
		"\2\2\u0174\u0175\7E\2\2\u0175\u0176\7q\2\2\u0176\u0177\7p\2\2\u0177\u0178"+
		"\7v\2\2\u0178\u0179\7c\2\2\u0179\u017a\7k\2\2\u017a\u017e\7p\2\2\u017b"+
		"\u017c\7\u5307\2\2\u017c\u017e\7\u542d\2\2\u017d\u0174\3\2\2\2\u017d\u017b"+
		"\3\2\2\2\u017e\60\3\2\2\2\u017f\u0180\7P\2\2\u0180\u0181\7q\2\2\u0181"+
		"\u0182\7v\2\2\u0182\u0183\7E\2\2\u0183\u0184\7q\2\2\u0184\u0185\7p\2\2"+
		"\u0185\u0186\7v\2\2\u0186\u0187\7c\2\2\u0187\u0188\7k\2\2\u0188\u018d"+
		"\7p\2\2\u0189\u018a\7\u4e0f\2\2\u018a\u018b\7\u5307\2\2\u018b\u018d\7"+
		"\u542d\2\2\u018c\u017f\3\2\2\2\u018c\u0189\3\2\2\2\u018d\62\3\2\2\2\u018e"+
		"\u018f\7G\2\2\u018f\u0190\7s\2\2\u0190\u0191\7w\2\2\u0191\u0192\7c\2\2"+
		"\u0192\u0193\7n\2\2\u0193\u0194\7u\2\2\u0194\u0195\7K\2\2\u0195\u0196"+
		"\7i\2\2\u0196\u0197\7p\2\2\u0197\u0198\7q\2\2\u0198\u0199\7t\2\2\u0199"+
		"\u019a\7g\2\2\u019a\u019b\7E\2\2\u019b\u019c\7c\2\2\u019c\u019d\7u\2\2"+
		"\u019d\u01a6\7g\2\2\u019e\u019f\7\u5fff\2\2\u019f\u01a0\7\u7567\2\2\u01a0"+
		"\u01a1\7\u5929\2\2\u01a1\u01a2\7\u5c11\2\2\u01a2\u01a3\7\u519b\2\2\u01a3"+
		"\u01a4\7\u7b4b\2\2\u01a4\u01a6\7\u4e90\2\2\u01a5\u018e\3\2\2\2\u01a5\u019e"+
		"\3\2\2\2\u01a6\64\3\2\2\2\u01a7\u01a8\7P\2\2\u01a8\u01a9\7q\2\2\u01a9"+
		"\u01aa\7v\2\2\u01aa\u01ab\7G\2\2\u01ab\u01ac\7s\2\2\u01ac\u01ad\7w\2\2"+
		"\u01ad\u01ae\7c\2\2\u01ae\u01af\7n\2\2\u01af\u01b0\7u\2\2\u01b0\u01b1"+
		"\7K\2\2\u01b1\u01b2\7i\2\2\u01b2\u01b3\7p\2\2\u01b3\u01b4\7q\2\2\u01b4"+
		"\u01b5\7t\2\2\u01b5\u01b6\7g\2\2\u01b6\u01b7\7E\2\2\u01b7\u01b8\7c\2\2"+
		"\u01b8\u01b9\7u\2\2\u01b9\u01c3\7g\2\2\u01ba\u01bb\7\u5fff\2\2\u01bb\u01bc"+
		"\7\u7567\2\2\u01bc\u01bd\7\u5929\2\2\u01bd\u01be\7\u5c11\2\2\u01be\u01bf"+
		"\7\u519b\2\2\u01bf\u01c0\7\u4e0f\2\2\u01c0\u01c1\7\u7b4b\2\2\u01c1\u01c3"+
		"\7\u4e90\2\2\u01c2\u01a7\3\2\2\2\u01c2\u01ba\3\2\2\2\u01c3\66\3\2\2\2"+
		"\u01c4\u01c5\t\2\2\2\u01c58\3\2\2\2\u01c6\u01c8\7/\2\2\u01c7\u01c6\3\2"+
		"\2\2\u01c7\u01c8\3\2\2\2\u01c8\u01c9\3\2\2\2\u01c9\u01ca\5C\"\2\u01ca"+
		"\u01cb\7\60\2\2\u01cb\u01cd\5C\"\2\u01cc\u01ce\5E#\2\u01cd\u01cc\3\2\2"+
		"\2\u01cd\u01ce\3\2\2\2\u01ce\u01da\3\2\2\2\u01cf\u01d1\7/\2\2\u01d0\u01cf"+
		"\3\2\2\2\u01d0\u01d1\3\2\2\2\u01d1\u01d2\3\2\2\2\u01d2\u01d3\5C\"\2\u01d3"+
		"\u01d4\5E#\2\u01d4\u01da\3\2\2\2\u01d5\u01d7\7/\2\2\u01d6\u01d5\3\2\2"+
		"\2\u01d6\u01d7\3\2\2\2\u01d7\u01d8\3\2\2\2\u01d8\u01da\5C\"\2\u01d9\u01c7"+
		"\3\2\2\2\u01d9\u01d0\3\2\2\2\u01d9\u01d6\3\2\2\2\u01da:\3\2\2\2\u01db"+
		"\u01dc\7v\2\2\u01dc\u01dd\7t\2\2\u01dd\u01de\7w\2\2\u01de\u01e5\7g\2\2"+
		"\u01df\u01e0\7h\2\2\u01e0\u01e1\7c\2\2\u01e1\u01e2\7n\2\2\u01e2\u01e3"+
		"\7u\2\2\u01e3\u01e5\7g\2\2\u01e4\u01db\3\2\2\2\u01e4\u01df\3\2\2\2\u01e5"+
		"<\3\2\2\2\u01e6\u01ea\5O(\2\u01e7\u01e9\5M\'\2\u01e8\u01e7\3\2\2\2\u01e9"+
		"\u01ec\3\2\2\2\u01ea\u01e8\3\2\2\2\u01ea\u01eb\3\2\2\2\u01eb>\3\2\2\2"+
		"\u01ec\u01ea\3\2\2\2\u01ed\u01ee\7$\2\2\u01ee\u01ef\5A!\2\u01ef\u01f0"+
		"\7$\2\2\u01f0@\3\2\2\2\u01f1\u01f4\5G$\2\u01f2\u01f4\n\3\2\2\u01f3\u01f1"+
		"\3\2\2\2\u01f3\u01f2\3\2\2\2\u01f4\u01f7\3\2\2\2\u01f5\u01f3\3\2\2\2\u01f5"+
		"\u01f6\3\2\2\2\u01f6B\3\2\2\2\u01f7\u01f5\3\2\2\2\u01f8\u01fa\5Q)\2\u01f9"+
		"\u01f8\3\2\2\2\u01fa\u01fb\3\2\2\2\u01fb\u01f9\3\2\2\2\u01fb\u01fc\3\2"+
		"\2\2\u01fcD\3\2\2\2\u01fd\u01ff\t\4\2\2\u01fe\u0200\t\5\2\2\u01ff\u01fe"+
		"\3\2\2\2\u01ff\u0200\3\2\2\2\u0200\u0201\3\2\2\2\u0201\u0202\5C\"\2\u0202"+
		"F\3\2\2\2\u0203\u0204\7^\2\2\u0204\u0208\t\6\2\2\u0205\u0208\5K&\2\u0206"+
		"\u0208\5I%\2\u0207\u0203\3\2\2\2\u0207\u0205\3\2\2\2\u0207\u0206\3\2\2"+
		"\2\u0208H\3\2\2\2\u0209\u020a\7^\2\2\u020a\u020b\4\62\65\2\u020b\u020c"+
		"\4\629\2\u020c\u0213\4\629\2\u020d\u020e\7^\2\2\u020e\u020f\4\629\2\u020f"+
		"\u0213\4\629\2\u0210\u0211\7^\2\2\u0211\u0213\4\629\2\u0212\u0209\3\2"+
		"\2\2\u0212\u020d\3\2\2\2\u0212\u0210\3\2\2\2\u0213J\3\2\2\2\u0214\u0215"+
		"\7^\2\2\u0215\u0216\7w\2\2\u0216\u0217\5S*\2\u0217\u0218\5S*\2\u0218\u0219"+
		"\5S*\2\u0219\u021a\5S*\2\u021aL\3\2\2\2\u021b\u0220\5O(\2\u021c\u0220"+
		"\t\7\2\2\u021d\u0220\5Q)\2\u021e\u0220\t\b\2\2\u021f\u021b\3\2\2\2\u021f"+
		"\u021c\3\2\2\2\u021f\u021d\3\2\2\2\u021f\u021e\3\2\2\2\u0220N\3\2\2\2"+
		"\u0221\u0223\t\t\2\2\u0222\u0221\3\2\2\2\u0223P\3\2\2\2\u0224\u0225\t"+
		"\n\2\2\u0225R\3\2\2\2\u0226\u0227\t\13\2\2\u0227T\3\2\2\2\u0228\u022a"+
		"\t\f\2\2\u0229\u0228\3\2\2\2\u022a\u022b\3\2\2\2\u022b\u0229\3\2\2\2\u022b"+
		"\u022c\3\2\2\2\u022c\u022d\3\2\2\2\u022d\u022e\b+\2\2\u022eV\3\2\2\2\u022f"+
		"\u0231\7\17\2\2\u0230\u022f\3\2\2\2\u0230\u0231\3\2\2\2\u0231\u0232\3"+
		"\2\2\2\u0232\u0233\7\f\2\2\u0233\u0234\3\2\2\2\u0234\u0235\b,\2\2\u0235"+
		"X\3\2\2\2\u0236\u0237\7\61\2\2\u0237\u0238\7,\2\2\u0238\u023c\3\2\2\2"+
		"\u0239\u023b\13\2\2\2\u023a\u0239\3\2\2\2\u023b\u023e\3\2\2\2\u023c\u023d"+
		"\3\2\2\2\u023c\u023a\3\2\2\2\u023d\u023f\3\2\2\2\u023e\u023c\3\2\2\2\u023f"+
		"\u0240\7,\2\2\u0240\u0241\7\61\2\2\u0241\u0242\3\2\2\2\u0242\u0243\b-"+
		"\2\2\u0243Z\3\2\2\2\u0244\u0245\7\61\2\2\u0245\u0246\7\61\2\2\u0246\u024a"+
		"\3\2\2\2\u0247\u0249\n\r\2\2\u0248\u0247\3\2\2\2\u0249\u024c\3\2\2\2\u024a"+
		"\u0248\3\2\2\2\u024a\u024b\3\2\2\2\u024b\u024e\3\2\2\2\u024c\u024a\3\2"+
		"\2\2\u024d\u024f\7\17\2\2\u024e\u024d\3\2\2\2\u024e\u024f\3\2\2\2\u024f"+
		"\u0250\3\2\2\2\u0250\u0251\7\f\2\2\u0251\u0252\3\2\2\2\u0252\u0253\b."+
		"\2\2\u0253\\\3\2\2\2,\2|\u0085\u00e5\u00ea\u00f2\u00f7\u00ff\u0105\u010c"+
		"\u0118\u0128\u0136\u0148\u0150\u015c\u0165\u0172\u017d\u018c\u01a5\u01c2"+
		"\u01c7\u01cd\u01d0\u01d6\u01d9\u01e4\u01ea\u01f3\u01f5\u01fb\u01ff\u0207"+
		"\u0212\u021f\u0222\u022b\u0230\u023c\u024a\u024e\3\2\3\2";
	public static final ATN _ATN =
		new ATNDeserializer().deserialize(_serializedATN.toCharArray());
	static {
		_decisionToDFA = new DFA[_ATN.getNumberOfDecisions()];
		for (int i = 0; i < _ATN.getNumberOfDecisions(); i++) {
			_decisionToDFA[i] = new DFA(_ATN.getDecisionState(i), i);
		}
	}
}