<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
		http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd
	">
	<import resource="classpath:urule-core-context.xml"/>
	
	<bean id="urule.knowledgePackageService" class="com.itheima.sfbx.rule.console.DefaultKnowledgePackageService">
		<property name="knowledgeBuilder" ref="urule.knowledgeBuilder"></property>
		<property name="repositoryService" ref="urule.repositoryService"></property>
	</bean>
	
	<bean id="urule.consoleServletHandler" class="com.itheima.sfbx.rule.console.servlet.console.ConsoleServletHandler">
		<property name="debugMessageHolder" ref="urule.debugMessageHolder"></property>
	</bean>
	
	<bean id="urule.consoleDebugWriter" class="com.itheima.sfbx.rule.console.servlet.console.ConsoleDebugWriter">
		<property name="debugMessageHolder" ref="urule.debugMessageHolder"></property>
	</bean>
	
	<bean id="urule.debugMessageHolder" class="com.itheima.sfbx.rule.console.servlet.console.DebugMessageHolder"></bean>
	
	<bean id="urule.httpSessionKnowledgeCache" class="com.itheima.sfbx.rule.console.servlet.respackage.HttpSessionKnowledgeCache"></bean>
	
	<bean id="urule.repositoryResourceProvider" class="com.itheima.sfbx.rule.console.repository.RepositoryResourceProvider">
		<property name="repositoryService" ref="urule.repositoryService"></property>
	</bean>
	<bean id="urule.permissionConfigServletHandler" class="com.itheima.sfbx.rule.console.servlet.permission.PermissionConfigServletHandler">
		<property name="repositoryService" ref="urule.repositoryService"></property>
		<property name="permissionStore" ref="urule.permissionService"></property>
	</bean>
	
	<bean id="urule.permissionService" class="com.itheima.sfbx.rule.console.repository.permission.PermissionServiceImpl">
		<property name="repositoryService" ref="urule.repositoryService"></property>
	</bean>
	
	<bean id="urule.scorecardEditorServletHandler" class="com.itheima.sfbx.rule.console.servlet.scorecard.ScorecardEditorServletHandler"></bean>
	
	<bean id="urule.resourceLoaderServletHandler" class="com.itheima.sfbx.rule.console.servlet.ResourceLoaderServletHandler"></bean>
	
	<bean id="urule.loadKnowledgeServletHandler" class="com.itheima.sfbx.rule.console.servlet.knowledge.LoadKnowledgeServletHandler">
		<property name="knowledgePackageService" ref="urule.knowledgePackageService"></property>
	</bean>
	
	<bean id="urule.xmlServletHandler" class="com.itheima.sfbx.rule.console.servlet.xml.XmlServletHandler">
		<property name="repositoryService" ref="urule.repositoryService"></property>
		<property name="builtInActionLibraryBuilder" ref="urule.builtInActionLibraryBuilder"></property>
	</bean>
	
	<bean id="urule.frameServletHandler" class="com.itheima.sfbx.rule.console.servlet.frame.FrameServletHandler">
		<property name="repositoryService" ref="urule.repositoryService"></property>
		<property name="welcomePage" value="${urule.welcomePage}"></property>
		<property name="title" value="${urule.console.title}"></property>
	</bean>
	<bean id="urule.packageServletHandler" class="com.itheima.sfbx.rule.console.servlet.respackage.PackageServletHandler">
		<property name="repositoryService" ref="urule.repositoryService"></property>
		<property name="knowledgeBuilder" ref="urule.knowledgeBuilder"></property>
		<property name="httpSessionKnowledgeCache" ref="urule.httpSessionKnowledgeCache"></property>
	</bean>
	
	<bean id="urule.reteDiagramServletHandler" class="com.itheima.sfbx.rule.console.servlet.diagram.ReteDiagramServletHandler">
		<property name="knowledgeBuilder" ref="urule.knowledgeBuilder"></property>
		<property name="httpSessionKnowledgeCache" ref="urule.httpSessionKnowledgeCache"></property>
	</bean>
	
	<bean id="urule.ruleSetServletEditorHandler" class="com.itheima.sfbx.rule.console.servlet.ruleset.RuleSetServletEditorHandler"></bean>
	<bean id="urule.decisionTreeEditorServletHandler" class="com.itheima.sfbx.rule.console.servlet.decisiontree.DecisionTreeEditorServletHandler"></bean>
	<bean id="urule.variableEditorServletHandler" class="com.itheima.sfbx.rule.console.servlet.variable.VariableEditorServletHandler"></bean>
	<bean id="urule.constantServletHandler" class="com.itheima.sfbx.rule.console.servlet.constant.ConstantServletHandler"></bean>
	<bean id="urule.parameterServletHandler" class="com.itheima.sfbx.rule.console.servlet.parameter.ParameterServletHandler"></bean>
	<bean id="urule.actionServletHandler" class="com.itheima.sfbx.rule.console.servlet.action.ActionServletHandler"></bean>
	<bean id="urule.ruleFlowDesignerServletHandler" class="com.itheima.sfbx.rule.console.servlet.flow.RuleFlowDesignerServletHandler">
		<property name="repositoryService" ref="urule.repositoryService"></property>
		<property name="flowDeserializer" ref="urule.flowDeserializer"></property>
	</bean>
	
	<bean id="urule.ulEditorServletHandler" class="com.itheima.sfbx.rule.console.servlet.ul.ULEditorServletHandler">
		<property name="repositoryService" ref="urule.repositoryService"></property>
		<property name="dslRuleSetBuilder" ref="urule.dslRuleSetBuilder"></property>
		<property name="resourceLibraryBuilder" ref="urule.resourceLibraryBuilder"></property>
	</bean>
	<bean id="urule.decisiontableEditorServletHandler" class="com.itheima.sfbx.rule.console.servlet.decisiontable.DecisiontableEditorServletHandler"></bean>
	<bean id="urule.scriptDecisiontableEditorServletHandler" class="com.itheima.sfbx.rule.console.servlet.scriptdecisiontable.ScriptDecisiontableEditorServletHandler"></bean>
	
	<bean id="urule.commonServletHandler" class="com.itheima.sfbx.rule.console.servlet.common.CommonServletHandler">
		<property name="repositoryService" ref="urule.repositoryService"></property>
		<property name="builtInActionLibraryBuilder" ref="urule.builtInActionLibraryBuilder"></property>
	</bean>
	<bean id="urule.clientConfigServletHandler" class="com.itheima.sfbx.rule.console.servlet.client.ClientConfigServletHandler">
		<property name="repositoryService" ref="urule.repositoryService"></property>
	</bean>
	
	<bean id="urule.console.propertyPlaceholderConfigurer" class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
		<property name="ignoreUnresolvablePlaceholders" value="true"></property>
		<property name="location">
			<value>classpath:urule-console-context.properties</value>
		</property>
	</bean>
	
	<bean id="urule.repositoryBuilder" class="com.itheima.sfbx.rule.console.repository.RepositoryBuilder" destroy-method="destroy">
		<property name="repositoryXml" value="${urule.repository.xml}"></property>
		<property name="repoHomeDir" value="${urule.repository.dir}"></property>
		<property name="repositoryDatasourceName" value="${urule.repository.datasourcename}"></property>
		<property name="databaseType" value="${urule.repository.databasetype}"></property>
	</bean>
	
	<bean id="urule.repositoryService" class="com.itheima.sfbx.rule.console.repository.RepositoryServiceImpl">
		<property name="repositoryBuilder" ref="urule.repositoryBuilder"></property>
		<property name="permissionService" ref="urule.permissionService"></property>
	</bean>
	
</beans>