#!/bin/bash

# Spring Boot应用启动脚本
# 用于解决Java 11+版本中的各种问题

# 设置JVM参数
JVM_ARGS="
-server
-Xms512m
-Xmx1024m
-XX:MetaspaceSize=256m
-XX:MaxMetaspaceSize=512m
--add-opens java.base/java.lang=ALL-UNNAMED
--add-opens java.base/java.lang.reflect=ALL-UNNAMED
--add-opens java.base/java.lang.invoke=ALL-UNNAMED
--add-opens java.base/java.util=ALL-UNNAMED
--add-opens java.base/java.net=ALL-UNNAMED
--add-opens java.base/java.io=ALL-UNNAMED
--add-opens java.base/java.security=ALL-UNNAMED
--add-opens java.base/sun.nio.ch=ALL-UNNAMED
--add-opens java.base/sun.security.util=ALL-UNNAMED
--add-opens java.management/sun.management=ALL-UNNAMED
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-Djava.awt.headless=true
-Dfile.encoding=UTF-8
-Duser.timezone=Asia/Shanghai
"

# 应用程序参数
APP_ARGS="
--spring.profiles.active=dev,mybatis
--server.shutdown=graceful
--spring.lifecycle.timeout-per-shutdown-phase=30s
"

# 检查参数
if [ $# -eq 0 ]; then
    echo "使用方法: $0 <jar文件路径>"
    echo "示例: $0 sfbx-dict/dict-web/target/dict-web.jar"
    exit 1
fi

JAR_FILE=$1

# 检查JAR文件是否存在
if [ ! -f "$JAR_FILE" ]; then
    echo "错误: JAR文件不存在: $JAR_FILE"
    exit 1
fi

echo "正在启动应用程序: $JAR_FILE"
echo "JVM参数: $JVM_ARGS"
echo "应用参数: $APP_ARGS"

# 启动应用程序
java $JVM_ARGS -jar "$JAR_FILE" $APP_ARGS
