# JVM启动参数配置文件
# 用于解决Java 11+版本中的反射访问警告

# 解决CGLIB反射访问警告
--add-opens java.base/java.lang=ALL-UNNAMED
--add-opens java.base/java.lang.reflect=ALL-UNNAMED
--add-opens java.base/java.lang.invoke=ALL-UNNAMED
--add-opens java.base/java.util=ALL-UNNAMED
--add-opens java.base/java.net=ALL-UNNAMED
--add-opens java.base/java.io=ALL-UNNAMED
--add-opens java.base/java.security=ALL-UNNAMED
--add-opens java.base/sun.nio.ch=ALL-UNNAMED
--add-opens java.base/sun.security.util=ALL-UNNAMED
--add-opens java.management/sun.management=ALL-UNNAMED

# 内存配置
-Xms512m
-Xmx1024m
-XX:MetaspaceSize=256m
-XX:MaxMetaspaceSize=512m

# GC配置
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200

# 其他优化参数
-Djava.awt.headless=true
-Dfile.encoding=UTF-8
-Duser.timezone=Asia/Shanghai
