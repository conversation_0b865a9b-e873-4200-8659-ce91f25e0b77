<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover">
    <title>店铺商品详情</title>
</head>

<body>
<div id="app">
    <h1>商品信息json</h1>
    <div>
        ${shopGoods}
    </div>
<#--    <div>-->
<#--        <h1>-->
<#--            封面图片列表-->
<#--        </h1>-->
<#--        <ul>-->
<#--            <li>路径: <a href=""> </a></li>-->
<#--        </ul>-->
<#--    </div>-->
<#--    <div>-->
<#--        <h1>-->
<#--            商品基本信息-->
<#--        </h1>-->
<#--        <p> 优惠价: <span> </span>  原价: <span> </span> </p>-->
<#--        -->
<#--        <p>标题: <span> </span></p>-->

<#--        <p>当前规格: </p>-->
<#--        -->
<#--        <p>所有规格: </p>-->

<#--        <p>服务规则列表:  </p>-->

<#--    </div>-->
<#--    <div>-->
<#--        <h1>-->
<#--            商品详情图片列表-->
<#--        </h1>-->
<#--        <ul>-->
<#--            <li>路径: <a href=""> </a></li>-->
<#--        </ul>-->
<#--    </div>-->

    <p>参数列表:  未提供</p>
    <p>评价列表:  异步ajax查询</p>
    <p>优惠券:  异步查询</p>
</div>
</body>
</html>
