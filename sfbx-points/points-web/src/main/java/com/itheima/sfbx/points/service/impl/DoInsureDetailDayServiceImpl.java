package com.itheima.sfbx.points.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.itheima.sfbx.points.mapper.DoInsureDetailDayMapper;
import com.itheima.sfbx.points.pojo.DoInsureDetailDay;
import com.itheima.sfbx.points.service.IDoInsureDetailDayService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
/**
 * @Description：日投保明细服务实现类
 */
@Slf4j
@Service
public class DoInsureDetailDayServiceImpl extends ServiceImpl<DoInsureDetailDayMapper, DoInsureDetailDay> implements IDoInsureDetailDayService {

}
