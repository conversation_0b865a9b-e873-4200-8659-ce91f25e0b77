package com.itheima.sfbx.points.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.itheima.sfbx.points.mapper.CategoryDpvMapper;
import com.itheima.sfbx.points.pojo.CategoryDpv;
import com.itheima.sfbx.points.service.ICategoryDpvService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
/**
 * @Description：日保险分类访问页面量服务实现类
 */
@Slf4j
@Service
public class CategoryDpvServiceImpl extends ServiceImpl<CategoryDpvMapper, CategoryDpv> implements ICategoryDpvService {

}
