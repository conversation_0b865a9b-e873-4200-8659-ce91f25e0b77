package com.itheima.sfbx.points.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.itheima.sfbx.points.mapper.DoInsureDuvMapper;
import com.itheima.sfbx.points.pojo.DoInsureDuv;
import com.itheima.sfbx.points.service.IDoInsureDuvService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
/**
 * @Description：日投保用户访问数服务实现类
 */
@Slf4j
@Service
public class DoInsureDuvServiceImpl extends ServiceImpl<DoInsureDuvMapper, DoInsureDuv> implements IDoInsureDuvService {

}
