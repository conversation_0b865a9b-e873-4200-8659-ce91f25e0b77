/*******************************************************************************
 * Copyright 2017 Bstek
 * 
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License.  You may obtain a copy
 * of the License at
 * 
 *   http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations under
 * the License.
 ******************************************************************************/
package com.itheima.sfbx.rule.console;


/**
 * <AUTHOR>
 * @since 2016年8月30日
 */
public class DefaultRepositoryInteceptor implements RepositoryInteceptor {

	@Override
	public void readFile(String file) {
		
	}

	@Override
	public void saveFile(String file, String content) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void createFile(String file,String content) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void deleteFile(String file) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void renameFile(String oldFileName, String newFileName) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void createDir(String dir) {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void createProject(String project) {
		// TODO Auto-generated method stub
		
	}
}
