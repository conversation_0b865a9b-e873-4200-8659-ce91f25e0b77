/*******************************************************************************
 * Copyright 2017 Bstek
 * 
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not
 * use this file except in compliance with the License.  You may obtain a copy
 * of the License at
 * 
 *   http://www.apache.org/licenses/LICENSE-2.0
 * 
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 * WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.  See the
 * License for the specific language governing permissions and limitations under
 * the License.
 ******************************************************************************/
package com.itheima.sfbx.rule.console.repository.model;

/**
 * <AUTHOR>
 * @since 2015年5月7日
 */
public enum PermissionType {
	ProjectVisible, NewVar, NewParam, NewConst, NewAction, 
	NewRule, NewDslRule, NewDecisionTable,NewDslDecisionTable,NewRuleFlow,NewDecisionTree,
	
	DelVar,DelParam,DelConst,DelAction,DelRule,DelDslRule,
	DelDecisionTable,DelDslDecisionTable,DelRuleFlow,DelDecisionTree,
	
	ModVar,ModParam,ModConst,ModAction,ModRule,ModDslRule,
	ModDecisionTable,ModDslDecisionTable,ModRuleFlow,ModDecisionTree,
	
	InsertRow, DelRow, InsertConditionCol, ModConditionCol, DelConditionCol,
	InsertActionCol, ModActionCol, DelActionCol,
	
	InsertDslRow, DelDslRow, InsertDslConditionCol, ModDslConditionCol, DelDslConditionCol,
	InsertDslActionCol, ModDslActionCol, DelDslActionCol
}
