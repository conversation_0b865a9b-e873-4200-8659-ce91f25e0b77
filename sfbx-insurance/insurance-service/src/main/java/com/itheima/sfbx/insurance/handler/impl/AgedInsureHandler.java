package com.itheima.sfbx.insurance.handler.impl;

import com.itheima.sfbx.framework.commons.constant.insure.InsureConstant;
import com.itheima.sfbx.insurance.dto.DoInsureVo;
import com.itheima.sfbx.insurance.dto.EarningVO;
import com.itheima.sfbx.insurance.handler.InsureHandler;
import org.springframework.stereotype.Service;

/**
 * @ClassName AgedInsureHandler.java
 * @Description 理财型养老保险投保、收益计算、保险订单生成
 */
@Service
public class AgedInsureHandler extends EarningsInsureHandler implements InsureHandler {

}
