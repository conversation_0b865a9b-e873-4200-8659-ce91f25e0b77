package com.itheima.sfbx.insurance.constant;

/**
 * @ClassName InsuranceConstant.java
 * @Description TODO
 */
public class InsuranceConstant {

    //单位：天
    public static final String DAY = "DAY";
    //单位：周
    public static final String WEEK = "WEEK";
    //单位：月
    public static final String MONTH = "MONTH";
    //单位：年
    public static final String YEAR = "YEAR";
    //单位：岁
    public static final String AGE = "AGE";
    //年龄范围
    public static final String RANGE_AGE = "XS005";
    //什么时候开始领
    public static final String ACTUAL_GET_START = "XS012";
    //合同规定：领至多久
    public static final String ACTUAL_GET_END = "XS013";
    //养老测算：领至多久
    public static final String TRIAL_GET_END ="XS015";
    //领取周期
    public static final String ACTUAL_GET_UNIT = "XS014";
    //理财：投入方式
    public static final String BUY_MODE = "XS002";
    //定投：追投方式投入
    public static final String BUY_MODE_0 = "XS002-0";
    //趸交：一次性投入
    public static final String BUY_MODE_1 = "XS002-1";
    //团险人数
    public static final String NUMBER_OF_PEOPLE = "XS016";
    //投入周期单位
    public static final String PERIODIC_UNIT = "XS011";
    //投入时长
    public static final String PERIODIC = "XS008";
    //保障期限
    public static final String PROTECTION_PERIOD = "XS006";
    //付款方式
    public static final String PAY_MENT = "XS003";
    //连续投保
    public static final String AUTO_WARRANTY_EXTENSION = "XS010";
    //金牌好品
    public static final String GOLDSELECTION_0="0";
    //上架状态
    public static final String INSURANCE_STATE_0="0";
}
