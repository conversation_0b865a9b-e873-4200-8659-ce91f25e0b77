package com.itheima.sfbx.insurance.enums;

import com.itheima.sfbx.framework.commons.enums.basic.IBaseEnum;

/**
* @ClassName RiskEnum.java
* @Description 风险表枚举
*/

public enum RiskEnum implements IBaseEnum {

    PAGE_FAIL(53001, "查询风险表分页失败"),
    LIST_FAIL(53002, "查询风险表列表失败"),
    FIND_ONE_FAIL(53003, "查询风险表对象失败"),
    SAVE_FAIL(53004, "保存风险表失败"),
    UPDATE_FAIL(53005, "修改风险表失败"),
    DEL_FAIL(53006, "删除风险表失败")
    ;

    private Integer code;

    private String msg;

    RiskEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

}
