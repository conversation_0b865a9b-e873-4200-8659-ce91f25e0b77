package com.itheima.sfbx.insurance.handler.impl;

import com.itheima.sfbx.framework.commons.constant.insure.InsureConstant;
import com.itheima.sfbx.insurance.handler.InsureHandler;
import org.springframework.stereotype.Service;

/**
 * @ClassName AccidentInsureHandler.java
 * @Description 保障型意外类：保险投保、创建订单、保费计算
 */
@Service
public class AccidentInsureHandler extends SafeguardInsureHandler implements InsureHandler {


}
