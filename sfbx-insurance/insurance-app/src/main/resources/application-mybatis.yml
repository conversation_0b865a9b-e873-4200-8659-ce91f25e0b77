# MyBatis-Plus配置
mybatis-plus:
  # 配置扫描通用枚举
  type-enums-package: com.itheima.sfbx.framework.commons.enums
  # 实体扫描，多个package用逗号或者分号分隔
  type-aliases-package: com.itheima.sfbx.insurance.pojo
  # 实体父类
  type-aliases-super-type: com.itheima.sfbx.framework.mybatisplus.basic.BasePojo
  # mapper文件扫描
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  # 全局配置
  global-config:
    # 数据库相关配置
    db-config:
      # 主键类型  AUTO:"数据库ID自增", INPUT:"用户输入ID", ID_WORKER:"全局唯一ID (数字类型唯一ID)", UUID:"全局唯一ID UUID";
      id-type: ASSIGN_ID
      # 字段策略 IGNORED:"忽略判断",NOT_NULL:"非 NULL 判断"),NOT_EMPTY:"非空判断"
      field-strategy: NOT_EMPTY
      # 驼峰下划线转换
      column-underline: true
      # 数据库大写下划线转换
      capital-mode: true
      # 逻辑删除配置
      logic-delete-field: dataState
      logic-delete-value: "1"
      logic-not-delete-value: "0"
  # 原生配置
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
