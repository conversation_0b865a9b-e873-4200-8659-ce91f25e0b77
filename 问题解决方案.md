# Spring Boot应用启动问题解决方案

## 问题概述

项目在启动过程中遇到了以下几个主要问题：

1. **非法反射访问警告** - CGLIB库在Java 11+版本中的反射访问问题
2. **MySQL驱动类过时警告** - 使用了已弃用的驱动类
3. **依赖注入问题** - MyBatis-Plus配置中的类路径错误
4. **内存泄漏警告** - 应用关闭时未正确清理资源

## 解决方案

### 1. MyBatis-Plus配置问题修复

**问题原因：** Nacos配置中心的`shared-mybatis-plus.yml`文件中包含错误的包名`com.itheima.bolee.framework.mybatisplus.basic.BasePojo`，实际应该是`com.itheima.sfbx.framework.mybatisplus.basic.BasePojo`。

**解决方案：**
- 为每个模块创建了本地的`application-mybatis.yml`配置文件
- 修改了各模块的`bootstrap.yml`文件，移除对错误Nacos配置的依赖
- 添加了`mybatis` profile到活跃配置中

**修改的文件：**
- `sfbx-dict/dict-web/src/main/resources/application-mybatis.yml`
- `sfbx-file/file-web/src/main/resources/application-mybatis.yml`
- `sfbx-insurance/insurance-app/src/main/resources/application-mybatis.yml`
- `sfbx-insurance/insurance-mgt/src/main/resources/application-mybatis.yml`
- `sfbx-points/points-web/src/main/resources/application-mybatis.yml`
- `sfbx-sms/sms-web/src/main/resources/application-mybatis.yml`
- `sfbx-trade/trade-web/src/main/resources/application-mybatis.yml`
- `sfbx-security/security-web/src/main/resources/application-mybatis.yml`

### 2. 非法反射访问警告解决

**问题原因：** CGLIB库在Java 11+版本中尝试通过反射访问受限制的类和方法。

**解决方案：**
- 创建了JVM启动参数配置文件`jvm-args.txt`
- 更新了Dockerfile文件，添加必要的`--add-opens`参数
- 创建了启动脚本`start-app.sh`和`start-app.bat`

**关键JVM参数：**
```
--add-opens java.base/java.lang=ALL-UNNAMED
--add-opens java.base/java.lang.reflect=ALL-UNNAMED
--add-opens java.base/java.lang.invoke=ALL-UNNAMED
```

### 3. MySQL驱动配置验证

**检查结果：** 项目已经使用了正确的MySQL驱动类`com.mysql.cj.jdbc.Driver`和MySQL 8.0.19版本，无需修改。

### 4. 内存泄漏问题解决

**问题原因：** 应用程序关闭时未正确清理Nacos客户端和其他后台线程。

**解决方案：**
- 创建了`GracefulShutdownConfig`配置类
- 实现了应用程序优雅关闭机制
- 添加了资源清理逻辑

## 使用说明

### 1. 启动应用程序

**Linux/Mac系统：**
```bash
chmod +x start-app.sh
./start-app.sh sfbx-dict/dict-web/target/dict-web.jar
```

**Windows系统：**
```cmd
start-app.bat sfbx-dict\dict-web\target\dict-web.jar
```

### 2. Docker部署

Dockerfile已经更新，包含了必要的JVM参数。直接使用`docker build`和`docker run`即可。

### 3. IDE开发环境

在IDE中运行时，请添加以下JVM参数：
```
-server
--add-opens java.base/java.lang=ALL-UNNAMED
--add-opens java.base/java.lang.reflect=ALL-UNNAMED
--add-opens java.base/java.lang.invoke=ALL-UNNAMED
```

并设置活跃配置文件为：`dev,mybatis`

## 验证方法

1. **启动应用程序** - 检查是否还有反射访问警告
2. **检查日志** - 确认MyBatis-Plus配置正确加载
3. **测试数据库连接** - 验证数据库操作正常
4. **优雅关闭** - 使用Ctrl+C关闭应用，检查是否有内存泄漏警告

## 注意事项

1. 确保所有模块都已编译并生成了JAR文件
2. 确保数据库服务正在运行且可访问
3. 确保Nacos服务正在运行且配置正确
4. 如果仍有问题，请检查具体的错误日志并相应调整配置
